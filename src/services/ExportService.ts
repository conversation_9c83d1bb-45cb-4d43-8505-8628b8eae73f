import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

export interface ExportColumn {
  key: string;
  label: string;
  visible: boolean;
}

export interface ExportOptions {
  columns: ExportColumn[];
  data: any[];
  filename?: string;
  title?: string;
  filters?: any;
}

export class ExportService {
  /**
   * Export data to PDF with customizable columns
   */
  public static exportToPDF(options: ExportOptions): void {
    const { columns, data, filename, title, filters } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      alert('Please select at least one column to export');
      return;
    }

    // Create new PDF document
    const doc = new jsPDF();

    // Add title
    const docTitle = title || 'Voter Export';
    doc.setFontSize(16);
    doc.setFont('helvetica', 'bold');
    doc.text(docTitle, 14, 20);

    // Add export info
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    const exportDate = new Date().toLocaleDateString('en-GB');
    const exportTime = new Date().toLocaleTimeString('en-GB');
    doc.text(`Exported on: ${exportDate} at ${exportTime}`, 14, 30);
    doc.text(`Total records: ${data.length}`, 14, 36);

    // Add filter info if available
    let yPosition = 42;
    if (filters) {
      const activeFilters = this.getActiveFilters(filters);
      if (activeFilters.length > 0) {
        doc.text('Applied filters:', 14, yPosition);
        yPosition += 6;
        activeFilters.forEach(filter => {
          doc.text(`• ${filter}`, 20, yPosition);
          yPosition += 5;
        });
        yPosition += 5;
      }
    }

    // Prepare table headers
    const headers = visibleColumns.map(col => col.label);

    // Prepare table data
    const tableData = data.map(row =>
      visibleColumns.map(col => this.formatCellValue(row[col.key]))
    );

    // Generate table
    autoTable(doc, {
      head: [headers],
      body: tableData,
      startY: yPosition,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [59, 130, 246], // Blue color
        textColor: 255,
        fontStyle: 'bold',
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252], // Light gray
      },
      columnStyles: this.getColumnStyles(visibleColumns),
      margin: { top: 10, right: 14, bottom: 20, left: 14 },
      didDrawPage: data => {
        // Add page numbers
        const pageCount = doc.getNumberOfPages();
        const pageSize = doc.internal.pageSize;
        const pageHeight = pageSize.height || pageSize.getHeight();

        doc.setFontSize(8);
        doc.text(`Page ${data.pageNumber} of ${pageCount}`, pageSize.width - 30, pageHeight - 10);
      },
    });

    // Save the PDF
    const pdfFilename = filename || `voters_export_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(pdfFilename);
  }

  /**
   * Export data to CSV with customizable columns
   */
  public static exportToCSV(options: ExportOptions): void {
    const { columns, data, filename } = options;

    // Filter only visible columns
    const visibleColumns = columns.filter(col => col.visible);

    if (visibleColumns.length === 0) {
      alert('Please select at least one column to export');
      return;
    }

    // Create CSV headers
    const headers = visibleColumns.map(col => col.label);

    // Create CSV rows
    const rows = data.map(row =>
      visibleColumns.map(col => {
        const value = this.formatCellValue(row[col.key]);
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (
          typeof value === 'string' &&
          (value.includes(',') || value.includes('"') || value.includes('\n'))
        ) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      })
    );

    // Combine headers and rows
    const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `voters_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Get active filters for display in PDF
   */
  private static getActiveFilters(filters: any): string[] {
    const activeFilters: string[] = [];

    if (filters.searchTerm) {
      activeFilters.push(`Search: "${filters.searchTerm}"`);
    }

    if (filters.gender && filters.gender !== 'All') {
      activeFilters.push(`Gender: ${filters.gender}`);
    }

    if (filters.ageFrom > 18 || filters.ageTo < 120) {
      activeFilters.push(`Age: ${filters.ageFrom}-${filters.ageTo} years`);
    }

    if (filters.community && filters.community !== 'All') {
      activeFilters.push(`Community: ${filters.community}`);
    }

    if (filters.religion && filters.religion !== 'All') {
      activeFilters.push(`Religion: ${filters.religion}`);
    }

    if (filters.economic_status && filters.economic_status !== 'All') {
      activeFilters.push(`Economic Status: ${filters.economic_status}`);
    }

    if (filters.status && filters.status !== 'All') {
      activeFilters.push(`Status: ${filters.status}`);
    }

    if (filters.polling_station && filters.polling_station !== 'All') {
      activeFilters.push(`Polling Station: ${filters.polling_station}`);
    }

    if (filters.section && filters.section !== 'All') {
      activeFilters.push(`Section: ${filters.section}`);
    }

    if (filters.supporter_status && filters.supporter_status !== 'All') {
      activeFilters.push(`Support Status: ${filters.supporter_status}`);
    }

    return activeFilters;
  }

  /**
   * Format cell values for export
   */
  private static formatCellValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }

    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }

    if (typeof value === 'number') {
      return value.toString();
    }

    return String(value).trim();
  }

  /**
   * Get column styles for PDF table
   */
  private static getColumnStyles(columns: ExportColumn[]): { [key: number]: any } {
    const styles: { [key: number]: any } = {};

    columns.forEach((col, index) => {
      switch (col.key) {
        case 'name':
          styles[index] = { cellWidth: 40 };
          break;
        case 'age':
          styles[index] = { cellWidth: 15, halign: 'center' };
          break;
        case 'gender':
          styles[index] = { cellWidth: 20, halign: 'center' };
          break;
        case 'epic':
          styles[index] = { cellWidth: 30, fontStyle: 'normal' };
          break;
        case 'pollingStation':
          styles[index] = { cellWidth: 35 };
          break;
        case 'status':
          styles[index] = { cellWidth: 20, halign: 'center' };
          break;
        default:
          styles[index] = { cellWidth: 'auto' };
      }
    });

    return styles;
  }

  /**
   * Get default column configuration
   */
  public static getDefaultColumns(): ExportColumn[] {
    return [
      { key: 'name', label: 'Name', visible: true },
      { key: 'age', label: 'Age', visible: true },
      { key: 'gender', label: 'Gender', visible: true },
      { key: 'epic', label: 'EPIC Number', visible: true },
      { key: 'pollingStation', label: 'Polling Station', visible: true },
      { key: 'affiliation', label: 'Affiliation', visible: false },
      { key: 'payee', label: 'Payee', visible: false },
      { key: 'status', label: 'Status', visible: true },
    ];
  }
}

export default ExportService;
