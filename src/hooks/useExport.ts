import { useCallback } from 'react';
import { useApp } from '../context/AppContext';
import { useFilteredVoters } from './useFilteredVoters';
import { ExportService } from '../services/ExportService';

export function useExport() {
  const { state } = useApp();
  const filteredVoters = useFilteredVoters(state.voters, state.filters);

  const exportToPDF = useCallback(() => {
    try {
      // Convert column visibility to export columns
      const columns = [
        { key: 'name', label: 'NAME', visible: state.columnVisibility.name },
        { key: 'age', label: 'AGE', visible: state.columnVisibility.age },
        { key: 'gender', label: 'GENDER', visible: state.columnVisibility.gender },
        { key: 'epic', label: 'EPIC NUMBER', visible: state.columnVisibility.epic },
        {
          key: 'pollingStation',
          label: 'POLLING STATION',
          visible: state.columnVisibility.pollingStation,
        },
        { key: 'affiliation', label: 'AFFILIATION', visible: state.columnVisibility.affiliation },
        { key: 'payee', label: 'PAYEE', visible: state.columnVisibility.payee },
        { key: 'status', label: 'STATUS', visible: state.columnVisibility.status },
      ];

      ExportService.exportToPDF({
        columns,
        data: filteredVoters,
        title: 'Voter Database Export',
        filters: state.filters,
        filename: `voters_export_${new Date().toISOString().split('T')[0]}.pdf`,
      });
    } catch (error) {
      throw new Error(
        `PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [state.columnVisibility, state.filters, filteredVoters]);

  const exportToCSV = useCallback(() => {
    try {
      // Convert column visibility to export columns
      const columns = [
        { key: 'name', label: 'NAME', visible: state.columnVisibility.name },
        { key: 'age', label: 'AGE', visible: state.columnVisibility.age },
        { key: 'gender', label: 'GENDER', visible: state.columnVisibility.gender },
        { key: 'epic', label: 'EPIC NUMBER', visible: state.columnVisibility.epic },
        {
          key: 'pollingStation',
          label: 'POLLING STATION',
          visible: state.columnVisibility.pollingStation,
        },
        { key: 'affiliation', label: 'AFFILIATION', visible: state.columnVisibility.affiliation },
        { key: 'payee', label: 'PAYEE', visible: state.columnVisibility.payee },
        { key: 'status', label: 'STATUS', visible: state.columnVisibility.status },
      ];

      ExportService.exportToCSV({
        columns,
        data: filteredVoters,
        filename: `voters_export_${new Date().toISOString().split('T')[0]}.csv`,
      });
    } catch (error) {
      throw new Error(
        `CSV export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [state.columnVisibility, filteredVoters]);

  return {
    exportToPDF,
    exportToCSV,
    recordCount: filteredVoters.length,
    visibleColumnCount: Object.values(state.columnVisibility).filter(Boolean).length,
  };
}
