import { useState, useEffect, useCallback } from 'react';
import {
  DatabaseService,
  VoterService,
  CSVImportService,
  VoterData,
  VoterFilters,
  CSVImportResult,
  Household,
} from '../database';

export interface DatabaseHookReturn {
  // State
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  voters: VoterData[];

  // Database operations
  initializeDatabase: () => Promise<void>;
  loadVoters: (filters?: VoterFilters) => Promise<void>;
  getHouseholds: (filters?: VoterFilters) => Promise<Household[]>;
  addVoter: (voter: VoterData) => Promise<number>;
  upsertVoter: (voter: VoterData) => Promise<{ id: number; isNew: boolean }>;
  updateVoter: (id: number, voter: Partial<VoterData>) => Promise<void>;
  deleteVoter: (id: number) => Promise<void>;

  // CSV operations
  importCSV: (file: File) => Promise<CSVImportResult>;
  exportCSV: (filters?: VoterFilters) => Promise<string>;
  validateCSV: (file: File) => Promise<{ valid: boolean; errors: string[] }>;

  // Database management
  exportDatabase: () => Uint8Array;
  importDatabase: (data: Uint8Array) => Promise<void>;
  clearDatabase: () => Promise<void>;
}

export const useDatabase = (): DatabaseHookReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [voters, setVoters] = useState<VoterData[]>([]);

  // Service instances
  const [dbService] = useState(() => DatabaseService.getInstance());
  const [voterService] = useState(() => new VoterService());
  const [csvService] = useState(() => new CSVImportService());

  /**
   * Initialize the database
   */
  const initializeDatabase = useCallback(async () => {
    if (isInitialized) return;

    setIsLoading(true);
    setError(null);

    try {
      await dbService.initialize({
        wasmUrl: '', // WASM file is in public directory
      });
      setIsInitialized(true);

      // Load voters immediately after initialization
      console.log('🔄 Database initialized, loading voters...');
      await loadVoters();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize database';
      setError(errorMessage);
      console.error('Database initialization failed:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, dbService]);

  /**
   * Load voters with optional filters
   */
  const loadVoters = useCallback(
    async (filters?: VoterFilters) => {
      if (!isInitialized) {
        console.log('⚠️ Database not initialized, skipping voter load');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const voterData = await voterService.getVoters(filters);
        console.log(`📊 Loaded ${voterData.length} voters from database`);
        setVoters(voterData);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load voters';
        setError(errorMessage);
        console.error('Failed to load voters:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, voterService]
  );

  /**
   * Add a new voter
   */
  const addVoter = useCallback(
    async (voter: VoterData): Promise<number> => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      setIsLoading(true);
      setError(null);

      try {
        const voterId = await voterService.addVoter(voter);
        // Reload voters to update the list
        await loadVoters();
        return voterId;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to add voter';
        setError(errorMessage);
        console.error('Failed to add voter:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, voterService, loadVoters]
  );

  /**
   * Get households grouped by relationship and house number
   */
  const getHouseholds = useCallback(
    async (filters?: VoterFilters): Promise<Household[]> => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      try {
        return await voterService.getHouseholds(filters);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to get households';
        setError(errorMessage);
        console.error('Failed to get households:', err);
        throw err;
      }
    },
    [isInitialized, voterService]
  );

  /**
   * Add or update a voter based on epic_number
   */
  const upsertVoter = useCallback(
    async (voter: VoterData): Promise<{ id: number; isNew: boolean }> => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      setIsLoading(true);
      setError(null);

      try {
        const result = await voterService.upsertVoter(voter);
        // Reload voters to update the list
        await loadVoters();
        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to upsert voter';
        setError(errorMessage);
        console.error('Failed to upsert voter:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, voterService, loadVoters]
  );

  /**
   * Update an existing voter
   */
  const updateVoter = useCallback(
    async (id: number, voter: Partial<VoterData>) => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      setIsLoading(true);
      setError(null);

      try {
        await voterService.updateVoter(id, voter);
        // Reload voters to update the list
        await loadVoters();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update voter';
        setError(errorMessage);
        console.error('Failed to update voter:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, voterService, loadVoters]
  );

  /**
   * Delete a voter
   */
  const deleteVoter = useCallback(
    async (id: number) => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      setIsLoading(true);
      setError(null);

      try {
        await voterService.deleteVoter(id);
        // Reload voters to update the list
        await loadVoters();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to delete voter';
        setError(errorMessage);
        console.error('Failed to delete voter:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, voterService, loadVoters]
  );

  /**
   * Import voters from CSV file
   */
  const importCSV = useCallback(
    async (file: File): Promise<CSVImportResult> => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      setIsLoading(true);
      setError(null);

      try {
        const result = await csvService.importFromFile(file);

        if (result.success) {
          // Reload voters to show imported data
          await loadVoters();
        }

        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to import CSV';
        setError(errorMessage);
        console.error('Failed to import CSV:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [isInitialized, csvService, loadVoters]
  );

  /**
   * Export voters to CSV
   */
  const exportCSV = useCallback(
    async (filters?: VoterFilters): Promise<string> => {
      if (!isInitialized) {
        throw new Error('Database not initialized');
      }

      try {
        return await csvService.exportToCSV(filters);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to export CSV';
        setError(errorMessage);
        console.error('Failed to export CSV:', err);
        throw err;
      }
    },
    [isInitialized, csvService]
  );

  /**
   * Validate CSV file structure
   */
  const validateCSV = useCallback(
    async (file: File): Promise<{ valid: boolean; errors: string[] }> => {
      try {
        return await csvService.validateCSVStructure(file);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to validate CSV';
        console.error('Failed to validate CSV:', err);
        return { valid: false, errors: [errorMessage] };
      }
    },
    [csvService]
  );

  /**
   * Export database as binary data
   */
  const exportDatabase = useCallback((): Uint8Array => {
    if (!isInitialized) {
      throw new Error('Database not initialized');
    }

    try {
      return dbService.exportDatabase();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export database';
      setError(errorMessage);
      console.error('Failed to export database:', err);
      throw err;
    }
  }, [isInitialized, dbService]);

  /**
   * Import database from binary data
   */
  const importDatabase = useCallback(
    async (data: Uint8Array) => {
      setIsLoading(true);
      setError(null);

      try {
        await dbService.importDatabase(data);
        setIsInitialized(true);
        // Reload voters after import
        await loadVoters();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to import database';
        setError(errorMessage);
        console.error('Failed to import database:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [dbService, loadVoters]
  );

  /**
   * Clear all database data
   */
  const clearDatabase = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      await dbService.clearDatabase();
      setVoters([]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear database';
      setError(errorMessage);
      console.error('Failed to clear database:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [dbService]);

  // Auto-initialize on mount
  useEffect(() => {
    if (!isInitialized && !isLoading) {
      initializeDatabase();
    }
  }, [isInitialized, isLoading, initializeDatabase]);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    voters,

    // Database operations
    initializeDatabase,
    loadVoters,
    getHouseholds,
    addVoter,
    upsertVoter,
    updateVoter,
    deleteVoter,

    // CSV operations
    importCSV,
    exportCSV,
    validateCSV,

    // Database management
    exportDatabase,
    importDatabase,
    clearDatabase,
  };
};
