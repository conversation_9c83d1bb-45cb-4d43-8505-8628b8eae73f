import './normalize.css';
import './styles.css';
import Sidebar from './components/Sidebar';
import VoterDetailPanel from './components/VoterDetailPanel';
import MainContent from './components/MainContent';
import { ThemeProvider } from './context/ThemeContext';
import { AppProvider } from './context/AppContext';
import { DialogProvider } from './components/DialogProvider';

function App() {
  return (
    <ThemeProvider>
      <AppProvider>
        <DialogProvider>
          <Sidebar />
          <MainContent />
          <VoterDetailPanel />
        </DialogProvider>
      </AppProvider>
    </ThemeProvider>
  );
}

export default App;
