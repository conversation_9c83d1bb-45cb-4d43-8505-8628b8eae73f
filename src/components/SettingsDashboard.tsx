import { useState, useRef, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { useLightSweep } from '../hooks/useLightSweep';
import { useDialogContext } from './DialogProvider';
import SettingsService from '../database/SettingsService';

function SettingsDashboard() {
  const { state, dispatch } = useApp();
  const { isSettingsDashboardOpen } = state;
  const { showConfirm } = useDialogContext();
  const containerRef = useRef<HTMLDivElement>(null);
  const triggerLightSweep = useLightSweep();
  const [selectedCategory, setSelectedCategory] = useState<string>('community');
  const [newItemInput, setNewItemInput] = useState('');
  const [categoryData, setCategoryData] = useState<{ [key: string]: string[] }>({
    community: [],
    religion: [],
    economic_status: [],
    education: [],
    occupation: [],
  });
  const [settingsService] = useState(() => new SettingsService());

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_SETTINGS_DASHBOARD' });
  };

  // Load category data from database
  const loadCategoryData = async () => {
    try {
      const [community, religion, economic_status, education, occupation] = await Promise.all([
        settingsService.getCategorySettings('community'),
        settingsService.getCategorySettings('religion'),
        settingsService.getCategorySettings('economic_status'),
        settingsService.getCategorySettings('education'),
        settingsService.getCategorySettings('occupation'),
      ]);

      setCategoryData({
        community,
        religion,
        economic_status,
        education,
        occupation,
      });
    } catch (error) {
      console.error('Failed to load category data:', error);
    }
  };

  // Load data when component mounts or panel opens
  useEffect(() => {
    if (isSettingsDashboardOpen) {
      loadCategoryData();
    }
  }, [isSettingsDashboardOpen]);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleAddItem = async () => {
    const trimmedItem = newItemInput.trim();
    if (trimmedItem) {
      try {
        const success = await settingsService.addCategorySetting(selectedCategory, trimmedItem);
        if (success) {
          setNewItemInput('');
          // Reload the category data
          await loadCategoryData();
        } else {
          showConfirm({
            title: 'Add Item Failed',
            message: 'Failed to add item. It may already exist.',
            variant: 'default',
            confirmText: 'OK',
            onConfirm: () => {},
          });
        }
      } catch (error) {
        console.error('Failed to add item:', error);
        showConfirm({
          title: 'Error',
          message: 'Failed to add item. Please try again.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    }
  };

  const handleRemoveItem = async (item: string) => {
    try {
      const success = await settingsService.removeCategorySetting(selectedCategory, item);
      if (success) {
        // Reload the category data
        await loadCategoryData();
      } else {
        showConfirm({
          title: 'Remove Item Failed',
          message: 'Failed to remove item.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    } catch (error) {
      console.error('Failed to remove item:', error);
      showConfirm({
        title: 'Error',
        message: 'Failed to remove item. Please try again.',
        variant: 'default',
        confirmText: 'OK',
        onConfirm: () => {},
      });
    }
  };

  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddItem();
    }
  };

  const handleEditItem = (item: string) => {
    setEditingItem(item);
    setEditValue(item);
  };

  const handleSaveEdit = async () => {
    if (!editingItem || !editValue.trim()) return;

    try {
      const success = await settingsService.updateCategorySetting(
        selectedCategory,
        editingItem,
        editValue.trim()
      );
      if (success) {
        setEditingItem(null);
        setEditValue('');
        await loadCategoryData();
      } else {
        showConfirm({
          title: 'Update Item Failed',
          message: 'Failed to update item. It may already exist.',
          variant: 'default',
          confirmText: 'OK',
          onConfirm: () => {},
        });
      }
    } catch (error) {
      console.error('Failed to update item:', error);
      showConfirm({
        title: 'Error',
        message: 'Failed to update item. Please try again.',
        variant: 'default',
        confirmText: 'OK',
        onConfirm: () => {},
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const showDeleteConfirmation = (item: string) => {
    showConfirm({
      title: 'Delete Category Item',
      message: `Are you sure you want to delete "${item}" from ${formatCategoryName(selectedCategory)}?\n\nThis action cannot be undone.`,
      variant: 'danger',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: () => {
        handleRemoveItem(item);
      },
    });
  };

  const formatCategoryName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ');
  };

  // Trigger light sweep when panel opens
  useEffect(() => {
    if (isSettingsDashboardOpen) {
      triggerLightSweep(containerRef.current);
    }
  }, [isSettingsDashboardOpen, triggerLightSweep]);

  return (
    <div
      ref={containerRef}
      className={`settings-dashboard-container ${isSettingsDashboardOpen ? 'is-open' : ''}`}
    >
      <div className="settings-dashboard">
        <div className="filter-header">
          <div className="filter-title">
            <svg
              className="dropdown-item-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M4 7V4h16v3" />
              <path d="M9 20h6" />
              <path d="M12 4v16" />
            </svg>
            Manage Data
          </div>
          <button onClick={handleClose} className="panel-close" aria-label="Close panel">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div className="settings-controls">
          <div className="filter-group">
            <label htmlFor="category-select">Category</label>
            <div className="select-wrapper">
              <select id="category-select" value={selectedCategory} onChange={handleCategoryChange}>
                <option value="community">Community</option>
                <option value="religion">Religion</option>
                <option value="economic_status">Economic Status</option>
              </select>
            </div>
          </div>
          <div className="add-item-form">
            <input
              type="text"
              id="new-item-input"
              placeholder="Enter new item"
              value={newItemInput}
              onChange={e => setNewItemInput(e.target.value)}
              onKeyDown={handleKeyDown}
            />
            <button onClick={handleAddItem} className="btn btn-primary">
              Add
            </button>
          </div>
        </div>
        <div className="data-list">
          <ul id="item-list">
            {categoryData[selectedCategory]?.map((item, index) => (
              <li key={index}>
                {editingItem === item ? (
                  <>
                    <input
                      type="text"
                      value={editValue}
                      onChange={e => setEditValue(e.target.value)}
                      className="edit-input"
                      autoFocus
                    />
                    <div className="item-actions">
                      <button onClick={handleSaveEdit} className="save-btn">
                        Save
                      </button>
                      <button onClick={handleCancelEdit} className="cancel-btn">
                        Cancel
                      </button>
                    </div>
                  </>
                ) : (
                  <>
                    <span className="item-text">{item}</span>
                    <div className="item-actions">
                      <button onClick={() => handleEditItem(item)} className="edit-btn">
                        Edit
                      </button>
                      <button onClick={() => showDeleteConfirmation(item)} className="delete-btn">
                        Delete
                      </button>
                    </div>
                  </>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

export default SettingsDashboard;
