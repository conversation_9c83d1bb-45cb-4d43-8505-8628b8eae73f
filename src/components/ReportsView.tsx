interface ReportsViewProps {
  onBackToMain: () => void;
}

function ReportsView({ onBackToMain }: ReportsViewProps) {
  return (
    <div id="reports-view">
      <div className="toolbar">
        <h1>Reports</h1>
        <div className="toolbar-left">
          <button onClick={onBackToMain} className="btn">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
            Back
          </button>
        </div>
      </div>
      <div className="reports-content">
        {/* Placeholder for charts */}
        <p>Chart placeholder</p>
      </div>
    </div>
  );
}

export default ReportsView;
