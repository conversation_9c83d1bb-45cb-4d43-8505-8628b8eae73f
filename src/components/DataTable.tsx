import React, { useState } from 'react';
import { useApp } from '../context/AppContext';
import { useFilteredVoters } from '../hooks/useFilteredVoters';
import { useClickOutside } from '../hooks/useClickOutside';

function DataTable() {
  const { state, dispatch } = useApp();
  const { voters, filters } = state;

  // Local state for columns dropdown
  const [isColumnsDropdownOpen, setIsColumnsDropdownOpen] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Use global column visibility state
  const { columnVisibility: visibleColumns } = state;

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 8;

  const filteredVoters = useFilteredVoters(voters, filters);

  // Calculate pagination
  const totalPages = Math.ceil(filteredVoters.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentVoters = filteredVoters.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  // Click outside handler for dropdown
  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    if (hasInteracted) {
      setIsColumnsDropdownOpen(false);
    }
  });

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Update search filter immediately for better React performance
    dispatch({ type: 'UPDATE_FILTER', payload: { key: 'searchTerm', value: e.target.value } });
  };

  const handleColumnsToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setHasInteracted(true);
    setIsColumnsDropdownOpen(prev => !prev);
  };

  const handleColumnToggle = (column: keyof typeof visibleColumns) => {
    dispatch({ type: 'TOGGLE_COLUMN', payload: column });
  };

  const handleVoterClick = (voter: any) => {
    dispatch({ type: 'TOGGLE_VOTER_PANEL', payload: voter });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const startPage = Math.max(1, currentPage - 2);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  return (
    <div className="data-table">
      <div className="table-header">
        <span className="table-title">Registered Voters</span>
        <div className="table-controls">
          <div className="search-container">
            <svg
              className="search-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
            <input
              type="text"
              className="search-input"
              placeholder="Search voters..."
              aria-label="Search voters"
              value={filters.searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="dropdown-container" ref={dropdownRef}>
            <button onClick={handleColumnsToggle} className="btn" aria-label="Show/hide columns">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="9" x2="9" y2="21"></line>
                <line x1="15" y1="9" x2="15" y2="21"></line>
              </svg>
              Columns
            </button>
            <div className={`dropdown-menu ${isColumnsDropdownOpen ? 'is-open' : ''}`}>
              {Object.entries(visibleColumns).map(([column, isVisible]) => (
                <div
                  key={column}
                  onClick={() => handleColumnToggle(column as keyof typeof visibleColumns)}
                  className={`dropdown-item ${isVisible ? 'is-selected' : ''}`}
                >
                  <svg
                    className="dropdown-item-icon"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    style={{ opacity: isVisible ? 1 : 0 }}
                  >
                    <path d="M20 6 9 17l-5-5" />
                  </svg>
                  <span>
                    {column.charAt(0).toUpperCase() + column.slice(1).replace(/([A-Z])/g, ' $1')}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            {visibleColumns.name && <th>NAME</th>}
            {visibleColumns.age && <th>AGE</th>}
            {visibleColumns.gender && <th>GENDER</th>}
            {visibleColumns.epic && <th>EPIC NUMBER</th>}
            {visibleColumns.pollingStation && <th>POLLING STATION</th>}
            {visibleColumns.affiliation && <th>AFFILIATION</th>}
            {visibleColumns.payee && <th>PAYEE</th>}
            {visibleColumns.status && <th>STATUS</th>}
          </tr>
        </thead>
        <tbody>
          {currentVoters.length > 0 ? (
            currentVoters.map(voter => (
              <tr
                key={voter.id}
                onClick={() => handleVoterClick(voter)}
                style={{ cursor: 'pointer' }}
              >
                {visibleColumns.name && <td className="name-cell">{voter.name}</td>}
                {visibleColumns.age && <td>{voter.age}</td>}
                {visibleColumns.gender && (
                  <td>
                    <span className={`gender-badge ${voter.gender.toLowerCase()}`}>
                      {voter.gender}
                    </span>
                  </td>
                )}
                {visibleColumns.epic && (
                  <td>
                    <span className="epic-code">{voter.epic}</span>
                  </td>
                )}
                {visibleColumns.pollingStation && <td>{voter.pollingStation}</td>}
                {visibleColumns.affiliation && <td>-</td>}
                {visibleColumns.payee && <td>-</td>}
                {visibleColumns.status && (
                  <td>
                    <span
                      className={`status-indicator status-${voter.status?.toLowerCase() || 'active'}`}
                    ></span>
                    {voter.status || 'Active'}
                  </td>
                )}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={Object.values(visibleColumns).filter(Boolean).length}
                className="empty-state"
              >
                <div className="empty-state-content">
                  <svg
                    width="48"
                    height="48"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.5"
                  >
                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                  </svg>
                  <h3>No voters found</h3>
                  <p>Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <div className="table-footer">
        <div className="table-info">
          {filteredVoters.length > 0 ? (
            <>
              Showing {startIndex + 1}-{Math.min(endIndex, filteredVoters.length)} of{' '}
              {filteredVoters.length} entries
              {filteredVoters.length !== voters.length && ` (filtered from ${voters.length} total)`}
            </>
          ) : (
            'No entries to show'
          )}
        </div>
        {totalPages > 1 && (
          <div className="pagination">
            <button
              className="page-btn"
              disabled={currentPage === 1}
              onClick={handlePreviousPage}
              aria-label="Previous page"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m15 18-6-6 6-6" />
              </svg>
            </button>

            {getPageNumbers().map(pageNum => (
              <button
                key={pageNum}
                className={`page-btn ${pageNum === currentPage ? 'is-active' : ''}`}
                onClick={() => handlePageChange(pageNum)}
                aria-current={pageNum === currentPage ? 'page' : undefined}
              >
                {pageNum}
              </button>
            ))}

            <button
              className="page-btn"
              disabled={currentPage === totalPages}
              onClick={handleNextPage}
              aria-label="Next page"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default DataTable;
