import { Database } from 'sql.js';
import DatabaseService from './DatabaseService';
import { handleDatabaseError, logError } from '../utils/errorHandling';
import { executeQuery, executeQuerySingle, executeUpdate, executeInsert } from '../utils/database';

/**
 * Base class for all database services providing common functionality
 */
export abstract class BaseService {
  protected dbService: DatabaseService;

  constructor() {
    this.dbService = DatabaseService.getInstance();
  }

  /**
   * Get database instance with validation
   */
  protected getDatabase(): Database {
    const db = this.dbService.getDatabase();
    if (!db) {
      throw new Error('Database not initialized. Please initialize the database first.');
    }
    return db;
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  protected async executeTransaction<T>(
    operation: (db: Database) => Promise<T>,
    operationName: string
  ): Promise<T> {
    const db = this.getDatabase();

    try {
      // Start transaction
      db.run('BEGIN TRANSACTION');

      // Execute operation
      const result = await operation(db);

      // Commit transaction
      db.run('COMMIT');

      // Save to localStorage after successful transaction
      this.dbService.saveToLocalStorage();

      return result;
    } catch (error) {
      // Rollback transaction on error
      try {
        db.run('ROLLBACK');
      } catch (rollbackError) {
        logError('Transaction Rollback', rollbackError);
      }

      handleDatabaseError(error, operationName);
    }
  }

  /**
   * Execute a query with proper error handling
   */
  protected executeQuery<T = any>(query: string, params: any[] = []): T[] {
    try {
      const db = this.getDatabase();
      return executeQuery<T>(db, query, params);
    } catch (error) {
      handleDatabaseError(error, `Query execution: ${query}`);
    }
  }

  /**
   * Execute a query and return single result
   */
  protected executeQuerySingle<T = any>(query: string, params: any[] = []): T | null {
    try {
      const db = this.getDatabase();
      return executeQuerySingle<T>(db, query, params);
    } catch (error) {
      handleDatabaseError(error, `Single query execution: ${query}`);
    }
  }

  /**
   * Execute an update/delete query
   */
  protected executeUpdate(query: string, params: any[] = []): number {
    try {
      const db = this.getDatabase();
      return executeUpdate(db, query, params);
    } catch (error) {
      handleDatabaseError(error, `Update execution: ${query}`);
    }
  }

  /**
   * Execute an insert query and return the inserted ID
   */
  protected executeInsert(query: string, params: any[] = []): number {
    try {
      const db = this.getDatabase();
      return executeInsert(db, query, params);
    } catch (error) {
      handleDatabaseError(error, `Insert execution: ${query}`);
    }
  }

  /**
   * Check if a record exists
   */
  protected recordExists(table: string, whereClause: string, params: any[] = []): boolean {
    try {
      const query = `SELECT 1 FROM ${table} WHERE ${whereClause} LIMIT 1`;
      const result = this.executeQuerySingle(query, params);
      return result !== null;
    } catch (error) {
      handleDatabaseError(error, `Record existence check: ${table}`);
    }
  }

  /**
   * Get count of records
   */
  protected getRecordCount(table: string, whereClause: string = '1=1', params: any[] = []): number {
    try {
      const query = `SELECT COUNT(*) as count FROM ${table} WHERE ${whereClause}`;
      const result = this.executeQuerySingle<{ count: number }>(query, params);
      return result?.count || 0;
    } catch (error) {
      handleDatabaseError(error, `Record count: ${table}`);
    }
  }

  /**
   * Validate required fields
   */
  protected validateRequiredFields(data: Record<string, any>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Sanitize and validate data before database operations
   */
  protected sanitizeData<T extends Record<string, any>>(data: T): T {
    const sanitized = { ...data } as any;

    // Sanitize string fields
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'string') {
        sanitized[key] = sanitized[key].trim();
        // Remove any null bytes that could cause issues
        sanitized[key] = sanitized[key].replace(/\0/g, '');
      }
    });

    return sanitized as T;
  }

  /**
   * Log operation for audit trail
   */
  protected logOperation(operation: string, details: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${this.constructor.name}] ${operation}:`, details);
    }
  }
}
