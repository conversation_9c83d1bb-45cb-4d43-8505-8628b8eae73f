import <PERSON> from 'papa<PERSON><PERSON>';
import VoterService, { VoterData } from './VoterService';
import { FILE_LIMITS } from '../utils/constants';
import { sanitizeString } from '../utils/validation';

export interface CSVImportResult {
  success: boolean;
  totalRows: number;
  importedRows: number;
  updatedRows: number;
  errors: string[];
  skippedRows: number;
}

export interface CSVVoterRow {
  name: string;
  relation_type: string;
  relation_name: string;
  house_number: string;
  birth_year: string;
  gender: string;
  epic_number: string;
  polling_station: string;
  section: string;
}

export class CSVImportService {
  private voterService: VoterService;

  constructor() {
    this.voterService = new VoterService();
  }

  /**
   * Import voters from CSV file
   */
  public async importFromFile(file: File): Promise<CSVImportResult> {
    return new Promise(resolve => {
      const result: CSVImportResult = {
        success: false,
        totalRows: 0,
        importedRows: 0,
        updatedRows: 0,
        errors: [],
        skippedRows: 0,
      };

      // Validate file size
      if (file.size > FILE_LIMITS.MAX_CSV_SIZE) {
        result.errors.push(
          `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${FILE_LIMITS.MAX_CSV_SIZE / 1024 / 1024}MB)`
        );
        resolve(result);
        return;
      }

      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        result.errors.push('Only CSV files are allowed');
        resolve(result);
        return;
      }

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: async parseResult => {
          try {
            result.totalRows = parseResult.data.length;

            // Process each row
            for (let i = 0; i < parseResult.data.length; i++) {
              const row = parseResult.data[i] as any;

              try {
                const voterData = this.mapCSVRowToVoterData(row);

                if (voterData) {
                  const upsertResult = await this.voterService.upsertVoter(voterData);
                  if (upsertResult.isNew) {
                    result.importedRows++;
                  } else {
                    result.updatedRows++;
                  }
                } else {
                  result.skippedRows++;
                }
              } catch (error) {
                result.errors.push(
                  `Row ${i + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`
                );
                result.skippedRows++;
              }
            }

            // Add parsing errors
            if (parseResult.errors && parseResult.errors.length > 0) {
              parseResult.errors.forEach(error => {
                result.errors.push(`Parse error: ${error.message} (Row: ${error.row})`);
              });
            }

            result.success = result.importedRows > 0;
            if (result.success) {
              this.voterService['dbService'].saveToLocalStorage();
            }
            resolve(result);
          } catch (error) {
            result.errors.push(
              `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
            resolve(result);
          }
        },
        error: error => {
          result.errors.push(`File parsing failed: ${error.message}`);
          resolve(result);
        },
      });
    });
  }

  /**
   * Import voters from CSV string
   */
  public async importFromString(csvString: string): Promise<CSVImportResult> {
    return new Promise(resolve => {
      const result: CSVImportResult = {
        success: false,
        totalRows: 0,
        importedRows: 0,
        updatedRows: 0,
        errors: [],
        skippedRows: 0,
      };

      Papa.parse(csvString, {
        header: true,
        skipEmptyLines: true,
        complete: async parseResult => {
          try {
            result.totalRows = parseResult.data.length;

            // Process each row
            for (let i = 0; i < parseResult.data.length; i++) {
              const row = parseResult.data[i] as any;

              try {
                const voterData = this.mapCSVRowToVoterData(row);

                if (voterData) {
                  const upsertResult = await this.voterService.upsertVoter(voterData);
                  if (upsertResult.isNew) {
                    result.importedRows++;
                  } else {
                    result.updatedRows++;
                  }
                } else {
                  result.skippedRows++;
                }
              } catch (error) {
                result.errors.push(
                  `Row ${i + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`
                );
                result.skippedRows++;
              }
            }

            // Add parsing errors
            if (parseResult.errors && parseResult.errors.length > 0) {
              parseResult.errors.forEach(error => {
                result.errors.push(`Parse error: ${error.message} (Row: ${error.row})`);
              });
            }

            result.success = result.importedRows > 0;

            if (result.success) {
              this.voterService['dbService'].saveToLocalStorage();
            }
            resolve(result);
          } catch (error) {
            result.errors.push(
              `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
            resolve(result);
          }
        },
        error: (error: any) => {
          result.errors.push(`String parsing failed: ${error.message}`);
          resolve(result);
        },
      });
    });
  }

  /**
   * Map CSV row to VoterData
   */
  private mapCSVRowToVoterData(row: any): VoterData | null {
    // Validate required fields
    if (!row.name || !row.epic_number) {
      throw new Error(`Missing required fields (name or epic_number)`);
    }

    // Clean and validate data with sanitization
    const name = sanitizeString(String(row.name));
    const epicNumber = sanitizeString(String(row.epic_number));

    if (!name || !epicNumber) {
      throw new Error(`Empty required fields after trimming`);
    }

    // Validate gender
    const gender = this.normalizeGender(row.gender);
    if (!gender) {
      throw new Error(`Invalid gender value: ${row.gender}`);
    }

    // Validate relationship type
    const relationshipType = this.normalizeRelationshipType(row.relation_type);

    // Parse birth year
    let birthYear: number | undefined;
    if (row.birth_year) {
      const year = parseInt(String(row.birth_year).trim());
      if (!isNaN(year) && year > 1900 && year <= new Date().getFullYear()) {
        birthYear = year;
      }
    }

    return {
      name,
      relationship_type: relationshipType,
      relationship_name: row.relation_name ? sanitizeString(String(row.relation_name)) : undefined,
      gender,
      birth_year: birthYear,
      epic_number: epicNumber,
      house_number: row.house_number ? sanitizeString(String(row.house_number)) : undefined,
      polling_station: row.polling_station
        ? sanitizeString(String(row.polling_station))
        : undefined,
      section: row.section ? sanitizeString(String(row.section)) : undefined,
      status: 'Active', // Default status for imported voters
    };
  }

  /**
   * Normalize gender values
   */
  private normalizeGender(gender: any): 'Male' | 'Female' | 'Other' | null {
    if (!gender) return null;

    const normalized = String(gender).trim().toLowerCase();

    switch (normalized) {
      case 'male':
      case 'm':
        return 'Male';
      case 'female':
      case 'f':
        return 'Female';
      case 'other':
      case 'o':
        return 'Other';
      default:
        return null;
    }
  }

  /**
   * Normalize relationship type values
   */
  private normalizeRelationshipType(
    relationType: any
  ): 'Father' | 'Mother' | 'Husband' | 'Others' | undefined {
    if (!relationType) return undefined;

    const normalized = String(relationType).trim().toLowerCase();

    switch (normalized) {
      case 'father':
      case 'f':
        return 'Father';
      case 'mother':
      case 'm':
        return 'Mother';
      case 'husband':
      case 'h':
        return 'Husband';
      case 'others':
      case 'other':
      case 'o':
        return 'Others';
      default:
        return 'Others'; // Default to Others for unknown values
    }
  }

  /**
   * Export voters to CSV
   */
  public async exportToCSV(filters?: any): Promise<string> {
    try {
      const voters = await this.voterService.getVoters(filters);

      const csvData = voters.map(voter => ({
        name: voter.name,
        relation_type: voter.relationship_type || '',
        relation_name: voter.relationship_name || '',
        house_number: voter.house_number || '',
        birth_year: voter.birth_year || '',
        gender: voter.gender,
        epic_number: voter.epic_number,
        polling_station: voter.polling_station || '',
        section: voter.section || '',
        phone: voter.phone || '',
        email: voter.email || '',
        status: voter.status || 'Active',
        community: voter.community || '',
        religion: voter.religion || '',
        economic_status: voter.economic_status || '',
        education: voter.education || '',
        occupation: voter.occupation || '',
        supporter_status: voter.supporter_status || '',
        custom_notes: voter.custom_notes || '',
      }));

      return Papa.unparse(csvData, {
        header: true,
        delimiter: ',',
        newline: '\n',
      });
    } catch (error) {
      console.error('Failed to export CSV:', error);
      throw error;
    }
  }

  /**
   * Validate CSV structure before import
   */
  public validateCSVStructure(file: File): Promise<{ valid: boolean; errors: string[] }> {
    return new Promise(resolve => {
      const result = { valid: true, errors: [] as string[] };

      Papa.parse(file, {
        header: true,
        preview: 1, // Only parse first row to check headers
        complete: parseResult => {
          const requiredHeaders = ['name', 'epic_number'];
          const optionalHeaders = [
            'relation_type',
            'relation_name',
            'house_number',
            'birth_year',
            'gender',
            'polling_station',
            'section',
          ];

          if (parseResult.data.length === 0) {
            result.valid = false;
            result.errors.push('CSV file is empty');
            resolve(result);
            return;
          }

          const headers = parseResult.meta.fields || [];

          // Check required headers
          requiredHeaders.forEach(header => {
            if (!headers.includes(header)) {
              result.valid = false;
              result.errors.push(`Missing required header: ${header}`);
            }
          });

          // Check for unknown headers (warn only)
          const allKnownHeaders = [...requiredHeaders, ...optionalHeaders];
          headers.forEach(header => {
            if (!allKnownHeaders.includes(header)) {
              result.errors.push(`Unknown header (will be ignored): ${header}`);
            }
          });

          resolve(result);
        },
        error: error => {
          result.valid = false;
          result.errors.push(`Failed to parse CSV: ${error.message}`);
          resolve(result);
        },
      });
    });
  }
}

export default CSVImportService;
