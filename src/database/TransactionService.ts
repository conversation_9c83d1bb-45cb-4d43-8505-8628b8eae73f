import { BaseService } from './BaseService';
import { validateTransaction } from '../utils/validation';

export interface Transaction {
  id?: number;
  voter_id: number;
  date: string; // DD-MM-YYYY format
  purpose: string;
  amount: number; // Integer, no decimals
  created_at?: string;
}

export class TransactionService extends BaseService {
  constructor() {
    super();
  }

  /**
   * Get all transactions for a voter
   */
  public async getTransactionsForVoter(voterId: number): Promise<Transaction[]> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('SELECT * FROM transactions WHERE voter_id = ? ORDER BY date DESC');
      stmt.bind([voterId]);

      const result: Transaction[] = [];
      while (stmt.step()) {
        const row = stmt.getAsObject();
        result.push({
          id: row.id as number,
          voter_id: row.voter_id as number,
          date: row.date as string,
          purpose: row.purpose as string,
          amount: row.amount as number,
          created_at: row.created_at as string,
        });
      }

      stmt.free();
      return result;
    } catch (error) {
      console.error('Failed to get transactions for voter:', error);
      throw error;
    }
  }

  /**
   * Add a new transaction
   */
  public async addTransaction(
    transaction: Omit<Transaction, 'id' | 'created_at'>
  ): Promise<number> {
    // Validate transaction data
    const validation = validateTransaction(transaction);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    // Sanitize data
    const sanitizedTransaction = this.sanitizeData(transaction);

    return this.executeTransaction(async () => {
      const insertId = this.executeInsert(
        'INSERT INTO transactions (voter_id, date, purpose, amount) VALUES (?, ?, ?, ?)',
        [
          sanitizedTransaction.voter_id,
          sanitizedTransaction.date,
          sanitizedTransaction.purpose,
          sanitizedTransaction.amount,
        ]
      );

      this.logOperation('Transaction Added', {
        id: insertId,
        voter_id: sanitizedTransaction.voter_id,
      });
      return insertId;
    }, 'Add Transaction');
  }

  /**
   * Update a transaction
   */
  public async updateTransaction(
    id: number,
    transaction: Omit<Transaction, 'id' | 'voter_id' | 'created_at'>
  ): Promise<void> {
    const db = this.getDatabase();

    try {
      // Validate transaction data (excluding voter_id for updates)
      const validation = validateTransaction({ ...transaction, voter_id: 1 }); // Dummy voter_id for validation
      if (!validation.isValid) {
        throw new Error(validation.errors.filter(e => !e.includes('Voter ID')).join(', '));
      }

      const stmt = db.prepare(`
        UPDATE transactions
        SET date = ?, purpose = ?, amount = ?
        WHERE id = ?
      `);

      stmt.run([transaction.date, transaction.purpose.trim(), transaction.amount, id]);

      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to update transaction:', error);
      throw error;
    }
  }

  /**
   * Delete a transaction
   */
  public async deleteTransaction(id: number): Promise<void> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('DELETE FROM transactions WHERE id = ?');
      stmt.run([id]);
      stmt.free();

      // Save to localStorage
      this.dbService.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      throw error;
    }
  }

  /**
   * Get total amount for a voter
   */
  public async getTotalForVoter(voterId: number): Promise<number> {
    const db = this.getDatabase();

    try {
      const stmt = db.prepare('SELECT SUM(amount) as total FROM transactions WHERE voter_id = ?');
      stmt.bind([voterId]);

      let total = 0;
      if (stmt.step()) {
        const row = stmt.getAsObject();
        total = (row.total as number) || 0;
      }

      stmt.free();
      return total;
    } catch (error) {
      console.error('Failed to get total for voter:', error);
      throw error;
    }
  }
}

export default TransactionService;
