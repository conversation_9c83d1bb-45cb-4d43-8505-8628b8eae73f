import initSqlJs, { Database, SqlJsStatic } from 'sql.js';

export interface DatabaseConfig {
  wasmUrl?: string;
}

export class DatabaseService {
  private static instance: DatabaseService;
  private SQL: SqlJsStatic | null = null;
  private db: Database | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Initialize the database service
   */
  public async initialize(config: DatabaseConfig = {}): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize sql.js
      this.SQL = await initSqlJs({
        locateFile: file => {
          if (config.wasmUrl !== undefined) {
            return config.wasmUrl ? `${config.wasmUrl}/${file}` : `/${file}`;
          }
          // Default to node_modules path for development
          return `/node_modules/sql.js/dist/${file}`;
        },
      });

      // Try to load existing database from localStorage
      const savedDb = this.loadFromLocalStorage();
      if (savedDb) {
        this.db = new this.SQL.Database(savedDb);

        // Check if we have data
        const result = this.db.exec('SELECT COUNT(*) as count FROM voters');
        const voterCount = result.length > 0 ? result[0].values[0][0] : 0;
        if (process.env.NODE_ENV === 'development') {
          console.log(`📊 Loaded database with ${voterCount} voters`);
        }
      } else {
        // Create new database
        this.db = new this.SQL.Database();
        if (process.env.NODE_ENV === 'development') {
          console.log('🆕 Created new database');
        }
      }

      // Create tables if they don't exist
      await this.createTables();

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  /**
   * Get the database instance
   */
  public getDatabase(): Database {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  /**
   * Save database to localStorage
   */
  public saveToLocalStorage(): void {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      const data = this.db.export();
      const binaryString = this.uint8ArrayToBinaryString(data);
      localStorage.setItem('electixir_database', binaryString);
    } catch (error) {
      console.error('❌ Failed to save database to localStorage:', error);

      // Check if localStorage is full
      if (error instanceof Error && error.name === 'QuotaExceededError') {
        console.error('💾 LocalStorage quota exceeded. Database is too large.');
        alert('Database is too large for localStorage. Consider exporting and clearing old data.');
      }

      throw error;
    }
  }

  /**
   * Load database from localStorage
   */
  private loadFromLocalStorage(): Uint8Array | null {
    try {
      const binaryString = localStorage.getItem('electixir_database');
      if (!binaryString) {
        return null;
      }
      return this.binaryStringToUint8Array(binaryString);
    } catch (error) {
      console.error('Failed to load database from localStorage:', error);
      return null;
    }
  }

  /**
   * Convert Uint8Array to binary string for localStorage
   */
  private uint8ArrayToBinaryString(arr: Uint8Array): string {
    const chunkSize = 0xffff;
    const strings: string[] = [];

    for (let i = 0; i * chunkSize < arr.length; i++) {
      const chunk = arr.subarray(i * chunkSize, (i + 1) * chunkSize);
      strings.push(String.fromCharCode.apply(null, Array.from(chunk)));
    }

    return strings.join('');
  }

  /**
   * Convert binary string back to Uint8Array
   */
  private binaryStringToUint8Array(str: string): Uint8Array {
    const arr = new Uint8Array(str.length);
    for (let i = 0; i < str.length; i++) {
      arr[i] = str.charCodeAt(i);
    }
    return arr;
  }

  /**
   * Create database tables
   */
  private async createTables(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const createTablesSQL = `
      -- Polling Stations table
      CREATE TABLE IF NOT EXISTS polling_stations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Sections table
      CREATE TABLE IF NOT EXISTS sections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        polling_station_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id),
        UNIQUE(name, polling_station_id)
      );

      -- Voters table (core data)
      CREATE TABLE IF NOT EXISTS voters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        relationship_type TEXT CHECK(relationship_type IN ('Father', 'Mother', 'Husband', 'Others')),
        relationship_name TEXT,
        gender TEXT CHECK(gender IN ('Male', 'Female', 'Other')),
        birth_year INTEGER,
        epic_number TEXT UNIQUE,
        house_number TEXT,
        polling_station_id INTEGER,
        section_id INTEGER,

        -- Extended contact information (optional)
        phone TEXT,
        email TEXT,
        facebook TEXT,
        instagram TEXT,
        twitter TEXT,

        -- Voter status (optional)
        status TEXT CHECK(status IN ('Active', 'Expired', 'Shifted', 'Duplicate', 'Missing', 'Disqualified')) DEFAULT 'Active',

        -- Political information (optional)
        supporter_status TEXT CHECK(supporter_status IN ('Strong Supporter', 'Potential Supporter', 'Undecided', 'Opposed')),

        -- Demographics (optional)
        education TEXT,
        occupation TEXT,
        community TEXT,
        religion TEXT,
        economic_status TEXT,
        custom_notes TEXT,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (polling_station_id) REFERENCES polling_stations(id),
        FOREIGN KEY (section_id) REFERENCES sections(id)
      );

      -- Transaction records table
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        voter_id INTEGER NOT NULL,
        date DATE NOT NULL,
        purpose TEXT NOT NULL,
        amount INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (voter_id) REFERENCES voters(id)
      );

      -- Settings table for custom values
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        value TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(category, value)
      );

      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_voters_epic ON voters(epic_number);
      CREATE INDEX IF NOT EXISTS idx_voters_name ON voters(name);
      CREATE INDEX IF NOT EXISTS idx_voters_polling_station ON voters(polling_station_id);
      CREATE INDEX IF NOT EXISTS idx_voters_section ON voters(section_id);
      CREATE INDEX IF NOT EXISTS idx_voters_status ON voters(status);
      CREATE INDEX IF NOT EXISTS idx_transactions_voter ON transactions(voter_id);
      CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(date);
    `;

    try {
      this.db.run(createTablesSQL);
    } catch (error) {
      console.error('Failed to create database tables:', error);
      throw error;
    }
  }

  /**
   * Export database as Uint8Array for download
   */
  public exportDatabase(): Uint8Array {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db.export();
  }

  /**
   * Import database from Uint8Array
   */
  public async importDatabase(data: Uint8Array): Promise<void> {
    if (!this.SQL) {
      throw new Error('SQL.js not initialized');
    }

    try {
      this.db = new this.SQL.Database(data);
      this.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to import database:', error);
      throw error;
    }
  }

  /**
   * Clear all data and reset database
   */
  public async clearDatabase(): Promise<void> {
    if (!this.SQL) {
      throw new Error('SQL.js not initialized');
    }

    try {
      this.db = new this.SQL.Database();
      await this.createTables();
      this.saveToLocalStorage();
    } catch (error) {
      console.error('Failed to clear database:', error);
      throw error;
    }
  }

  /**
   * Check database integrity
   */
  public checkIntegrity(): { isValid: boolean; errors: string[] } {
    if (!this.db) {
      return { isValid: false, errors: ['Database not initialized'] };
    }

    try {
      const result = this.db.exec('PRAGMA integrity_check');
      const errors: string[] = [];

      if (result.length > 0) {
        result[0].values.forEach(row => {
          const message = row[0] as string;
          if (message !== 'ok') {
            errors.push(message);
          }
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown integrity check error'],
      };
    }
  }
}

export default DatabaseService;
