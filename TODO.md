1. When importing multiple CSV files, they should be imported and displayed sequentially by number or alphabet.

User Feedback: I imported 20 CSV files with number in the file name. The files were imported but not in the order of the file name. This was the sequence 1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 2, 20, 3, 4 etc.

2. We have used styled Dialog to replace system prompts but there are some areas where we are still using system prompts.

User Feedback: In Manage Data when I try to delete a Category I still see the system prompt.

3. Left Sidebar next to POLLING STATION we need a toggle button to select/deselect the checkbox for all polling stations. Simple button defoid of style sayin All/None. Keep it minimal.

4. Left Sidebar Polling station under every Polling station there is Section called Unassigned where we know the voter belongs to a particular polling station but we dont know the section (yet). Now there is a small issue when i check unchecheck sections all works but checking Unassigned does not work. Actually the first time i uncheck it works but subsequently it fails. I suspect this is because it has the same name and problaby id's are generate with the name. Please analyze the issue and fix.