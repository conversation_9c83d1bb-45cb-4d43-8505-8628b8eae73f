{"name": "electixir", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "lint": "npx eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "npx eslint . --ext ts,tsx --fix", "format": "npx prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "npx prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "duplication": "npx jscpd src/", "quality:check": "npm run lint && npm run format:check && npm run duplication", "quality:fix": "npm run lint:fix && npm run format"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "papaparse": "^5.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "sql.js": "^1.13.0"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tauri-apps/cli": "^2", "@types/jspdf": "^2.0.0", "@types/jszip": "^3.4.1", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sql.js": "^1.4.9", "@vitejs/plugin-react": "^4.3.4", "globals": "^16.3.0", "typescript": "~5.6.2", "vite": "^6.0.3"}}